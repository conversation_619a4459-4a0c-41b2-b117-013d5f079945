{"name": "animagen-figma-plugin", "version": "2.0.0", "description": "Export Figma frames to AnimaGen for creating animated slideshows", "main": "dist/code.js", "scripts": {"dev": "concurrently \"npm run build:watch\" \"npm run serve\"", "build": "webpack --mode=production", "build:dev": "webpack --mode=development", "build:watch": "webpack --mode=development --watch", "serve": "webpack serve --mode=development", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "package": "npm run build && npm run package:zip", "package:zip": "cd dist && zip -r ../animagen-plugin.zip .", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["figma", "plugin", "animation", "slideshow", "export", "animagen"], "author": "AnimaGen Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/GsusFC/anima.git", "directory": "figma-plugin"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@figma/plugin-typings": "^1.75.0", "@types/jest": "^29.5.0", "@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.21", "concurrently": "^8.2.0", "css-loader": "^6.8.1", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.6.0", "jest-environment-jsdom": "^29.6.0", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.27", "postcss-loader": "^7.3.3", "rimraf": "^5.0.1", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}