.loadingScreen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: var(--figma-space-20);
  background-color: var(--figma-color-bg);
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--figma-color-border);
  border-top: 2px solid var(--figma-color-bg-brand);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--figma-space-12);
}

.message {
  font-size: var(--figma-font-size-14);
  color: var(--figma-color-text-secondary);
  margin: 0;
  text-align: center;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
