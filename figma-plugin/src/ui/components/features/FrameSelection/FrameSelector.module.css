.frameSelector {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--figma-color-bg);
}

.header {
  padding: var(--figma-space-16);
  border-bottom: 1px solid var(--figma-color-border);
  background-color: var(--figma-color-bg);
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--figma-space-12);
}

.title h3 {
  font-size: var(--figma-font-size-14);
  font-weight: var(--figma-font-weight-semibold);
  margin: 0;
  color: var(--figma-color-text);
}

.refreshButton {
  background: none;
  border: none;
  font-size: var(--figma-font-size-14);
  cursor: pointer;
  padding: var(--figma-space-4);
  border-radius: var(--figma-border-radius-small);
  transition: background-color 0.2s ease;
}

.refreshButton:hover {
  background-color: var(--figma-color-bg-secondary);
}

.controls {
  display: flex;
  flex-direction: column;
  gap: var(--figma-space-8);
}

.filterControls {
  display: flex;
  gap: var(--figma-space-8);
}

.select {
  flex: 1;
  padding: var(--figma-space-6) var(--figma-space-8);
  border: 1px solid var(--figma-color-border);
  border-radius: var(--figma-border-radius-small);
  font-size: var(--figma-font-size-11);
  background-color: var(--figma-color-bg);
  color: var(--figma-color-text);
}

.select:focus {
  outline: none;
  border-color: var(--figma-color-bg-brand);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.selectionControls {
  display: flex;
  gap: var(--figma-space-8);
}

.selectButton {
  flex: 1;
  padding: var(--figma-space-6) var(--figma-space-12);
  background-color: var(--figma-color-bg-secondary);
  color: var(--figma-color-text);
  border: 1px solid var(--figma-color-border);
  border-radius: var(--figma-border-radius-small);
  font-size: var(--figma-font-size-11);
  font-weight: var(--figma-font-weight-medium);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.selectButton:hover:not(:disabled) {
  background-color: var(--figma-color-bg-tertiary);
}

.selectButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.frameList {
  flex: 1;
  overflow-y: auto;
  padding: var(--figma-space-8);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: var(--figma-color-text-secondary);
}

.emptyIcon {
  font-size: 32px;
  margin-bottom: var(--figma-space-12);
}

.emptyState h4 {
  font-size: var(--figma-font-size-14);
  font-weight: var(--figma-font-weight-semibold);
  margin: 0 0 var(--figma-space-4) 0;
  color: var(--figma-color-text);
}

.emptyState p {
  font-size: var(--figma-font-size-12);
  margin: 0;
  line-height: 1.4;
}

.frameItem {
  display: flex;
  align-items: center;
  gap: var(--figma-space-12);
  padding: var(--figma-space-12);
  border: 1px solid var(--figma-color-border);
  border-radius: var(--figma-border-radius-medium);
  margin-bottom: var(--figma-space-8);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--figma-color-bg);
}

.frameItem:hover:not(.invalid) {
  border-color: var(--figma-color-bg-brand);
  background-color: var(--figma-color-bg-secondary);
}

.frameItem.selected {
  border-color: var(--figma-color-bg-brand);
  background-color: rgba(0, 102, 204, 0.1);
}

.frameItem.invalid {
  opacity: 0.6;
  cursor: not-allowed;
  border-color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.05);
}

.frameCheckbox {
  flex-shrink: 0;
}

.frameCheckbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.frameCheckbox input[type="checkbox"]:disabled {
  cursor: not-allowed;
}

.frameInfo {
  flex: 1;
  min-width: 0;
}

.frameName {
  display: flex;
  align-items: center;
  gap: var(--figma-space-8);
  font-size: var(--figma-font-size-12);
  font-weight: var(--figma-font-weight-medium);
  color: var(--figma-color-text);
  margin-bottom: var(--figma-space-4);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.invalidBadge {
  background-color: #e74c3c;
  color: white;
  font-size: var(--figma-font-size-11);
  font-weight: var(--figma-font-weight-medium);
  padding: var(--figma-space-2) var(--figma-space-6);
  border-radius: var(--figma-border-radius-small);
  flex-shrink: 0;
}

.frameDetails {
  display: flex;
  align-items: center;
  gap: var(--figma-space-12);
  font-size: var(--figma-font-size-11);
  color: var(--figma-color-text-secondary);
}

.dimensions {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.size {
  font-weight: var(--figma-font-weight-medium);
}

.complexity {
  font-weight: var(--figma-font-weight-semibold);
  text-transform: capitalize;
}

.frameActions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.order {
  font-size: var(--figma-font-size-11);
  color: var(--figma-color-text-tertiary);
  background-color: var(--figma-color-bg-secondary);
  padding: var(--figma-space-2) var(--figma-space-6);
  border-radius: var(--figma-border-radius-small);
  font-weight: var(--figma-font-weight-medium);
}

.selectionSummary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--figma-space-12) var(--figma-space-16);
  border-top: 1px solid var(--figma-color-border);
  background-color: var(--figma-color-bg-secondary);
  font-size: var(--figma-font-size-11);
}

.selectionCount {
  font-weight: var(--figma-font-weight-semibold);
  color: var(--figma-color-text);
}

.estimatedSize {
  color: var(--figma-color-text-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .controls {
    gap: var(--figma-space-6);
  }
  
  .filterControls {
    flex-direction: column;
  }
  
  .frameDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--figma-space-4);
  }
  
  .selectionSummary {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--figma-space-4);
  }
}
