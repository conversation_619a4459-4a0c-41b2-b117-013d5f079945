.authSetup {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--figma-color-bg);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--figma-space-16) var(--figma-space-20);
  border-bottom: 1px solid var(--figma-color-border);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--figma-space-8);
}

.logoIcon {
  font-size: 20px;
}

.logo h1 {
  font-size: var(--figma-font-size-16);
  font-weight: var(--figma-font-weight-semibold);
  margin: 0;
  color: var(--figma-color-text);
}

.version {
  font-size: var(--figma-font-size-11);
  color: var(--figma-color-text-tertiary);
  background-color: var(--figma-color-bg-secondary);
  padding: var(--figma-space-2) var(--figma-space-8);
  border-radius: var(--figma-border-radius-small);
}

.content {
  flex: 1;
  padding: var(--figma-space-20);
  overflow-y: auto;
}

.expiredNotice {
  display: flex;
  align-items: flex-start;
  gap: var(--figma-space-12);
  padding: var(--figma-space-12);
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--figma-border-radius-medium);
  margin-bottom: var(--figma-space-16);
}

.expiredIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.expiredNotice strong {
  display: block;
  font-weight: var(--figma-font-weight-semibold);
  margin-bottom: var(--figma-space-4);
}

.expiredNotice p {
  margin: 0;
  font-size: var(--figma-font-size-12);
  color: var(--figma-color-text-secondary);
}

.welcomeMessage {
  text-align: center;
  margin-bottom: var(--figma-space-24);
}

.welcomeMessage h2 {
  font-size: var(--figma-font-size-18);
  font-weight: var(--figma-font-weight-semibold);
  margin: 0 0 var(--figma-space-8) 0;
  color: var(--figma-color-text);
}

.welcomeMessage p {
  font-size: var(--figma-font-size-12);
  color: var(--figma-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.setupSteps {
  display: flex;
  flex-direction: column;
  gap: var(--figma-space-20);
}

.step {
  display: flex;
  gap: var(--figma-space-12);
}

.stepNumber {
  width: 24px;
  height: 24px;
  background-color: var(--figma-color-bg-brand);
  color: var(--figma-color-text-onbrand);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--figma-font-size-12);
  font-weight: var(--figma-font-weight-semibold);
  flex-shrink: 0;
}

.stepContent {
  flex: 1;
}

.stepContent h3 {
  font-size: var(--figma-font-size-14);
  font-weight: var(--figma-font-weight-semibold);
  margin: 0 0 var(--figma-space-4) 0;
  color: var(--figma-color-text);
}

.stepContent p {
  font-size: var(--figma-font-size-12);
  color: var(--figma-color-text-secondary);
  margin: 0 0 var(--figma-space-12) 0;
  line-height: 1.4;
}

.inputGroup {
  display: flex;
  gap: var(--figma-space-4);
  margin-bottom: var(--figma-space-8);
}

.input {
  flex: 1;
  padding: var(--figma-space-8) var(--figma-space-12);
  border: 1px solid var(--figma-color-border);
  border-radius: var(--figma-border-radius-medium);
  font-size: var(--figma-font-size-12);
  font-family: var(--figma-font-family);
  background-color: var(--figma-color-bg);
  color: var(--figma-color-text);
}

.input:focus {
  outline: none;
  border-color: var(--figma-color-bg-brand);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.input:disabled {
  background-color: var(--figma-color-bg-secondary);
  color: var(--figma-color-text-tertiary);
  cursor: not-allowed;
}

.inputError {
  border-color: #e74c3c;
}

.toggleButton {
  padding: var(--figma-space-8);
  border: 1px solid var(--figma-color-border);
  border-radius: var(--figma-border-radius-medium);
  background-color: var(--figma-color-bg-secondary);
  cursor: pointer;
  font-size: var(--figma-font-size-12);
}

.toggleButton:hover {
  background-color: var(--figma-color-bg-tertiary);
}

.toggleButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error {
  color: #e74c3c;
  font-size: var(--figma-font-size-11);
  margin-bottom: var(--figma-space-8);
}

.primaryButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--figma-space-8);
  width: 100%;
  padding: var(--figma-space-12) var(--figma-space-16);
  background-color: var(--figma-color-bg-brand);
  color: var(--figma-color-text-onbrand);
  border: none;
  border-radius: var(--figma-border-radius-medium);
  font-size: var(--figma-font-size-12);
  font-weight: var(--figma-font-weight-medium);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.primaryButton:hover:not(:disabled) {
  background-color: var(--figma-color-bg-brand-hover);
}

.primaryButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.secondaryButton {
  padding: var(--figma-space-8) var(--figma-space-12);
  background-color: var(--figma-color-bg-secondary);
  color: var(--figma-color-text);
  border: 1px solid var(--figma-color-border);
  border-radius: var(--figma-border-radius-medium);
  font-size: var(--figma-font-size-12);
  font-weight: var(--figma-font-weight-medium);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.secondaryButton:hover {
  background-color: var(--figma-color-bg-tertiary);
}

.spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.footer {
  margin-top: var(--figma-space-24);
  padding-top: var(--figma-space-16);
  border-top: 1px solid var(--figma-color-border);
}

.securityNote {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--figma-space-8);
  font-size: var(--figma-font-size-11);
  color: var(--figma-color-text-tertiary);
}

.securityIcon {
  font-size: 12px;
}
