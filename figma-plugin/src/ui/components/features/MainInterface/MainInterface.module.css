.mainInterface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--figma-color-bg);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--figma-space-12) var(--figma-space-16);
  border-bottom: 1px solid var(--figma-color-border);
  background-color: var(--figma-color-bg);
}

.userInfo {
  display: flex;
  align-items: center;
  gap: var(--figma-space-8);
}

.userAvatar {
  width: 24px;
  height: 24px;
  background-color: var(--figma-color-bg-brand);
  color: var(--figma-color-text-onbrand);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--figma-font-size-11);
  font-weight: var(--figma-font-weight-semibold);
}

.userDetails {
  display: flex;
  flex-direction: column;
}

.userName {
  font-size: var(--figma-font-size-12);
  font-weight: var(--figma-font-weight-medium);
  color: var(--figma-color-text);
  line-height: 1.2;
}

.userPlan {
  font-size: var(--figma-font-size-11);
  color: var(--figma-color-text-secondary);
  line-height: 1.2;
}

.connectionStatus {
  display: flex;
  align-items: center;
  gap: var(--figma-space-4);
}

.statusIndicator {
  width: 8px;
  height: 8px;
  background-color: #27ae60;
  border-radius: 50%;
}

.statusText {
  font-size: var(--figma-font-size-11);
  color: var(--figma-color-text-secondary);
}

.content {
  flex: 1;
  padding: var(--figma-space-20);
  overflow-y: auto;
}

.placeholder {
  text-align: center;
  padding: var(--figma-space-32);
}

.placeholder h2 {
  font-size: var(--figma-font-size-18);
  font-weight: var(--figma-font-weight-semibold);
  margin: 0 0 var(--figma-space-12) 0;
  color: var(--figma-color-text);
}

.placeholder p {
  font-size: var(--figma-font-size-12);
  color: var(--figma-color-text-secondary);
  margin: 0 0 var(--figma-space-12) 0;
  line-height: 1.4;
}

.placeholder ul {
  text-align: left;
  display: inline-block;
  margin: 0;
  padding-left: var(--figma-space-16);
}

.placeholder li {
  font-size: var(--figma-font-size-12);
  color: var(--figma-color-text-secondary);
  margin-bottom: var(--figma-space-4);
}
