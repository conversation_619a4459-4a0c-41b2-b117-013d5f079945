/* Global styles following Figma Design System */

:root {
  /* Figma UI Colors */
  --figma-color-bg: #ffffff;
  --figma-color-bg-secondary: #f8f9fa;
  --figma-color-bg-tertiary: #f1f3f4;
  --figma-color-bg-brand: #0066cc;
  --figma-color-bg-brand-hover: #0052a3;
  
  --figma-color-text: #1a1a1a;
  --figma-color-text-secondary: #666666;
  --figma-color-text-tertiary: #999999;
  --figma-color-text-onbrand: #ffffff;
  
  --figma-color-border: #e1e5e9;
  --figma-color-border-strong: #d1d5db;
  
  /* Figma UI Typography */
  --figma-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --figma-font-size-11: 11px;
  --figma-font-size-12: 12px;
  --figma-font-size-14: 14px;
  --figma-font-size-16: 16px;
  --figma-font-size-18: 18px;
  
  --figma-font-weight-normal: 400;
  --figma-font-weight-medium: 500;
  --figma-font-weight-semibold: 600;
  
  /* Figma UI Spacing */
  --figma-space-2: 2px;
  --figma-space-4: 4px;
  --figma-space-8: 8px;
  --figma-space-12: 12px;
  --figma-space-16: 16px;
  --figma-space-20: 20px;
  --figma-space-24: 24px;
  --figma-space-32: 32px;
  
  /* Figma UI Border Radius */
  --figma-border-radius-small: 4px;
  --figma-border-radius-medium: 6px;
  --figma-border-radius-large: 8px;
  
  /* Figma UI Shadows */
  --figma-shadow-small: 0 1px 2px rgba(0, 0, 0, 0.1);
  --figma-shadow-medium: 0 2px 4px rgba(0, 0, 0, 0.1);
  --figma-shadow-large: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Dark theme support */
[data-theme="dark"] {
  --figma-color-bg: #2c2c2c;
  --figma-color-bg-secondary: #3c3c3c;
  --figma-color-bg-tertiary: #4c4c4c;
  --figma-color-text: #ffffff;
  --figma-color-text-secondary: #cccccc;
  --figma-color-text-tertiary: #999999;
  --figma-color-border: #5c5c5c;
  --figma-color-border-strong: #6c6c6c;
}

/* Base styles following Figma conventions */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--figma-font-family);
  font-size: var(--figma-font-size-12);
  color: var(--figma-color-text);
  background-color: var(--figma-color-bg);
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Figma plugin container */
.app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Common utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-8 {
  gap: var(--figma-space-8);
}

.gap-12 {
  gap: var(--figma-space-12);
}

.gap-16 {
  gap: var(--figma-space-16);
}

.p-8 {
  padding: var(--figma-space-8);
}

.p-12 {
  padding: var(--figma-space-12);
}

.p-16 {
  padding: var(--figma-space-16);
}

.p-20 {
  padding: var(--figma-space-20);
}

.m-8 {
  margin: var(--figma-space-8);
}

.m-12 {
  margin: var(--figma-space-12);
}

.m-16 {
  margin: var(--figma-space-16);
}

.mb-8 {
  margin-bottom: var(--figma-space-8);
}

.mb-12 {
  margin-bottom: var(--figma-space-12);
}

.mb-16 {
  margin-bottom: var(--figma-space-16);
}

.mt-8 {
  margin-top: var(--figma-space-8);
}

.mt-12 {
  margin-top: var(--figma-space-12);
}

.mt-16 {
  margin-top: var(--figma-space-16);
}

/* Typography */
.text-11 {
  font-size: var(--figma-font-size-11);
}

.text-12 {
  font-size: var(--figma-font-size-12);
}

.text-14 {
  font-size: var(--figma-font-size-14);
}

.text-16 {
  font-size: var(--figma-font-size-16);
}

.text-18 {
  font-size: var(--figma-font-size-18);
}

.font-medium {
  font-weight: var(--figma-font-weight-medium);
}

.font-semibold {
  font-weight: var(--figma-font-weight-semibold);
}

/* Colors */
.text-secondary {
  color: var(--figma-color-text-secondary);
}

.text-tertiary {
  color: var(--figma-color-text-tertiary);
}

.bg-secondary {
  background-color: var(--figma-color-bg-secondary);
}

.bg-tertiary {
  background-color: var(--figma-color-bg-tertiary);
}

/* Borders */
.border {
  border: 1px solid var(--figma-color-border);
}

.border-strong {
  border: 1px solid var(--figma-color-border-strong);
}

.border-radius-small {
  border-radius: var(--figma-border-radius-small);
}

.border-radius-medium {
  border-radius: var(--figma-border-radius-medium);
}

.border-radius-large {
  border-radius: var(--figma-border-radius-large);
}

/* Shadows */
.shadow-small {
  box-shadow: var(--figma-shadow-small);
}

.shadow-medium {
  box-shadow: var(--figma-shadow-medium);
}

.shadow-large {
  box-shadow: var(--figma-shadow-large);
}
