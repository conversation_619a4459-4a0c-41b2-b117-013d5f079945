<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>AnimaGen Exporter</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  <!-- Figma UI styles -->
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 12px;
      background: var(--figma-color-bg, #ffffff);
      color: var(--figma-color-text, #000000);
      overflow: hidden;
    }
    
    #root {
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Loading state */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-size: 14px;
      color: var(--figma-color-text-secondary, #666);
    }
    
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid var(--figma-color-border, #e1e5e9);
      border-top: 2px solid var(--figma-color-bg-brand, #0066cc);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
    
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="loading">
      <div class="loading-spinner"></div>
      Loading AnimaGen Exporter...
    </div>
  </div>
  
  <!-- React app will be injected here by webpack -->
</body>
</html>
