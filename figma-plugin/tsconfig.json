{"compilerOptions": {"target": "ES5", "lib": ["ES5", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "paths": {"@/*": ["*"], "@ui/*": ["ui/*"], "@plugin/*": ["plugin/*"], "@shared/*": ["shared/*"]}, "types": ["@figma/plugin-typings", "node", "jest"]}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "dist", "tests"]}