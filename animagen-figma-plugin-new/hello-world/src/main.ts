import { showUI, on, once, emit } from '@create-figma-plugin/utilities'

export default function () {
  console.log('🚀 AnimaGen Plugin Starting...')

  // Show UI
  showUI({
    width: 400,
    height: 600,
    title: 'AnimaGen Exporter'
  })

  // Set up message handlers
  setupMessageHandlers()

  // Send initial data to UI
  sendInitialData()
}

function setupMessageHandlers() {
  // Handle UI ready
  once('ui-ready', () => {
    console.log('🎨 UI is ready')
    sendInitialData()
  })

  // Handle authentication
  on('authenticate', (data: { apiKey: string }) => {
    handleAuthentication(data.apiKey)
  })

  // Handle frame detection
  on('detect-frames', () => {
    detectAndSendFrames()
  })

  // Handle frame export
  on('export-frames', (data: { frameIds: string[], settings: any }) => {
    handleFrameExport(data.frameIds, data.settings)
  })
}

function sendInitialData() {
  // Detect frames immediately
  detectAndSendFrames()

  // Send initial auth state
  emit('auth-state-changed', {
    authenticated: false,
    loading: false,
    requiresSetup: true
  })
}

function detectAndSendFrames() {
  console.log('🔍 Detecting selected frames...')

  // Only get frames that are currently selected by the user
  const frames = figma.currentPage.selection.filter(node =>
    node.type === 'FRAME' &&
    node.width > 0 &&
    node.height > 0
  ) as FrameNode[]

  console.log(`👆 User has ${figma.currentPage.selection.length} items selected`)
  console.log(`🎯 Found ${frames.length} selected frames`)

  if (frames.length === 0) {
    console.log('⚠️ No frames selected. Please select frames to export.')
  } else {
    console.log('📋 Selected frame names:', frames.map(f => f.name))
  }

  const detectedFrames = frames.map((frame, index) => {
    const complexity = estimateComplexity(frame)

    return {
      id: frame.id,
      name: frame.name,
      width: Math.round(frame.width),
      height: Math.round(frame.height),
      x: Math.round(frame.x),
      y: Math.round(frame.y),
      order: index,
      complexity,
      isValidForExport: true, // Already filtered above
      estimatedSize: estimateFileSize(frame.width, frame.height, complexity),
      selected: false,
      visible: frame.visible,
      locked: frame.locked,
      aspectRatio: frame.width / frame.height
    }
  })

  console.log(`✅ Found ${detectedFrames.length} frames`)

  emit('frames-detected', { frames: detectedFrames })
}

function estimateComplexity(frame: FrameNode): 'low' | 'medium' | 'high' {
  const childCount = frame.children.length
  const hasEffects = frame.effects && frame.effects.length > 0
  const hasComplexFills = frame.fills && (frame.fills as readonly Paint[]).some(fill => fill.type !== 'SOLID')

  let score = 0
  if (childCount > 10) score += 1
  if (childCount > 25) score += 1
  if (hasEffects) score += 1
  if (hasComplexFills) score += 1

  if (score <= 1) return 'low'
  if (score <= 2) return 'medium'
  return 'high'
}

function estimateFileSize(width: number, height: number, complexity: string): string {
  const pixels = width * height
  let multiplier = 3 // Base RGB

  switch (complexity) {
    case 'medium': multiplier = 4; break
    case 'high': multiplier = 6; break
  }

  const bytes = pixels * multiplier

  if (bytes < 1024 * 1024) {
    return `${Math.round(bytes / 1024)}KB`
  } else {
    return `${Math.round(bytes / (1024 * 1024))}MB`
  }
}

function handleAuthentication(apiKey: string) {
  console.log('🔐 Handling authentication with key:', apiKey)

  // Simple validation for demo
  if (apiKey && apiKey.startsWith('ag_figma_')) {
    console.log('✅ Authentication successful')

    const authData = {
      type: 'auth-state-changed',
      data: {
        authenticated: true,
        loading: false,
        user: {
          id: 'test_user_123',
          email: '<EMAIL>',
          name: 'Test User',
          plan: 'Pro'
        },
        apiKey: apiKey
      }
    }

    console.log('📤 Sending auth success message:', authData.data)
    emit('auth-state-changed', authData.data)
  } else {
    console.log('❌ Authentication failed')

    const authData = {
      type: 'auth-state-changed',
      data: {
        authenticated: false,
        loading: false,
        error: 'Invalid API key. Please use a key starting with "ag_figma_"',
        requiresSetup: true
      }
    }

    console.log('📤 Sending auth error message:', authData.data)
    emit('auth-state-changed', authData.data)
  }
}

async function handleFrameExport(frameIds: string[], settings: any) {
  console.log('📤 Starting frame export...')

  // Send progress update
  figma.ui.postMessage({
    type: 'export-progress',
    data: {
      stage: 'preparing',
      current: 0,
      total: frameIds.length,
      message: 'Preparing export...',
      exportId: `export_${Date.now()}`,
      percentage: 0
    }
  })

  const results = []
  const exportId = `export_${Date.now()}`

  for (let i = 0; i < frameIds.length; i++) {
    const frameId = frameIds[i]
    const frame = figma.getNodeById(frameId) as FrameNode

    if (!frame || frame.type !== 'FRAME') {
      console.warn(`⚠️ Frame ${frameId} not found or invalid`)
      continue
    }

    // Send progress update
    figma.ui.postMessage({
      type: 'export-progress',
      data: {
        stage: 'exporting',
        current: i + 1,
        total: frameIds.length,
        message: `Exporting "${frame.name}"...`,
        frameName: frame.name,
        exportId: exportId,
        percentage: Math.round(((i + 1) / frameIds.length) * 100)
      }
    })

    try {
      // Export frame
      const imageData = await frame.exportAsync({
        format: settings.format || 'PNG',
        constraint: {
          type: 'SCALE',
          value: settings.scale || 2
        }
      })

      results.push({
        frameId,
        frameName: frame.name,
        success: true,
        imageData: Array.from(imageData),
        fileSize: imageData.length,
        order: i,
        exportTime: Date.now()
      })

      console.log(`✅ Exported frame: ${frame.name}`)

    } catch (error) {
      console.error(`❌ Failed to export frame ${frame.name}:`, error)

      results.push({
        frameId,
        frameName: frame.name,
        success: false,
        error: (error as Error).message,
        order: i,
        exportTime: Date.now()
      })
    }

    // Small delay to prevent overwhelming Figma
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  // Send completion
  figma.ui.postMessage({
    type: 'export-complete',
    data: {
      success: true,
      exportId: exportId,
      results: results,
      framesExported: results.filter(r => r.success).length,
      framesTotal: results.length,
      failedFrames: results.filter(r => !r.success).map(r => r.frameName),
      exportTime: Date.now(),
      settings: settings
    }
  })

  console.log('🎉 Export completed!')
}
